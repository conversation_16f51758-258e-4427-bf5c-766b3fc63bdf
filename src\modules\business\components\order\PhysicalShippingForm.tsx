import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Typography,
  FormItem,
  Input,
  Select,
  Textarea,
} from '@/shared/components/common';
import type { ShippingDto } from '../../types/order.types';

interface PhysicalShippingFormProps {
  shipping?: ShippingDto;
  onShippingChange: (shipping: ShippingDto) => void;
  customerAddress?: string;
}

/**
 * Form thông tin vận chuyển sản phẩm vật lý
 */
const PhysicalShippingForm: React.FC<PhysicalShippingFormProps> = ({
  shipping,
  onShippingChange,
  customerAddress,
}) => {
  const { t } = useTranslation(['business', 'common']);

  // Shipping method options
  const shippingMethodOptions = [
    { value: 'standard', label: t('business:order.shipping.standard') },
    { value: 'express', label: t('business:order.shipping.express') },
    { value: 'overnight', label: t('business:order.shipping.overnight') },
    { value: 'pickup', label: t('business:order.shipping.pickup') },
  ];

  // <PERSON><PERSON> lý thay đổi địa chỉ giao hàng
  const handleAddressChange = (address: string) => {
    onShippingChange({
      ...shipping,
      address,
    });
  };

  // Xử lý thay đổi ghi chú vận chuyển
  const handleNotesChange = (notes: string) => {
    onShippingChange({
      ...shipping,
      notes,
    });
  };

  // Xử lý thay đổi người nhận
  const handleRecipientNameChange = (recipientName: string) => {
    onShippingChange({
      ...shipping,
      recipientName,
    });
  };

  // Xử lý thay đổi số điện thoại người nhận
  const handleRecipientPhoneChange = (recipientPhone: string) => {
    onShippingChange({
      ...shipping,
      recipientPhone,
    });
  };

  return (
    <Card>
      <div className="p-6">
        <div className="flex items-center gap-2 mb-4">
          <Typography variant="h6">
            {t('business:order.physicalShipping')}
          </Typography>
        </div>

        <div className="space-y-4">
          {/* Phương thức vận chuyển */}
          <FormItem
            label={t('business:order.shipping.method')}
            required
          >
            <Select
              value={shipping?.method || ''}
              options={shippingMethodOptions}
              placeholder={t('business:order.shipping.selectMethod')}
              fullWidth
            />
          </FormItem>

          {/* Thông tin người nhận */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormItem
              label={t('business:order.shipping.recipientName')}
              required
            >
              <Input
                value={shipping?.recipientName || ''}
                onChange={(e) => handleRecipientNameChange(e.target.value)}
                placeholder={t('business:order.shipping.recipientNamePlaceholder')}
                fullWidth
              />
            </FormItem>

            <FormItem
              label={t('business:order.shipping.recipientPhone')}
              required
            >
              <Input
                value={shipping?.recipientPhone || ''}
                onChange={(e) => handleRecipientPhoneChange(e.target.value)}
                placeholder={t('business:order.shipping.recipientPhonePlaceholder')}
                fullWidth
              />
            </FormItem>
          </div>

          {/* Địa chỉ giao hàng */}
          <FormItem
            name='shippingAddress'
            label={t('business:order.shipping.address')}
            required
          >
            <div>
              <Textarea
                value={shipping?.address || customerAddress || ''}
                onChange={(e) => handleAddressChange(e.target.value)}
                placeholder={t('business:order.shipping.addressPlaceholder')}
                rows={3}
                fullWidth
              />
              {customerAddress && (
                <Typography variant="caption" className="text-gray-500 mt-1">
                  {t('business:order.shipping.customerAddressHint')}
                </Typography>
              )}
            </div>
          </FormItem>

          {/* Ghi chú vận chuyển */}
          <FormItem
            label={t('business:order.shipping.notes')}
          >
            <Textarea
              value={shipping?.notes || ''}
              onChange={(e) => handleNotesChange(e.target.value)}
              placeholder={t('business:order.shipping.notesPlaceholder')}
              rows={2}
              fullWidth
            />
          </FormItem>

          {/* Thông tin phí vận chuyển (nếu có) */}
          {shipping?.cost && (
            <div className="p-3 bg-gray-50 rounded-lg">
              <Typography variant="subtitle2" className="mb-1">
                {t('business:order.shipping.estimatedCost')}
              </Typography>
              <Typography variant="body1" className="font-semibold text-green-600">
                {new Intl.NumberFormat('vi-VN', {
                  style: 'currency',
                  currency: 'VND'
                }).format(shipping.cost)}
              </Typography>
            </div>
          )}
        </div>
      </div>
    </Card>
  );
};

export default PhysicalShippingForm;

