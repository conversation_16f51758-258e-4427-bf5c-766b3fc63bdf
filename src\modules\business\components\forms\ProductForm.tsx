import React, { useState, useRef, useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Form,
  FormItem,
  Input,
  Select,
  Textarea,
  ConditionalField,
  Typography,
  Chip,
  IconCard,
  CollapsibleCard,
  FormMultiWrapper,
} from '@/shared/components/common';
import AsyncSelectWithPagination from '@/shared/components/common/Select/AsyncSelectWithPagination';
import { Controller } from 'react-hook-form';
import { WarehouseService } from '../../services/warehouse.service';

import { ConditionType } from '@/shared/hooks/useFieldCondition';
import { z } from 'zod';
import {
  PriceTypeEnum,
  HasPriceDto,
  StringPriceDto,
  CreateProductDto,
  CreateProductResponse,
  ProductDto,
} from '../../types/product.types';
// import { useCreateProduct } from '../../hooks/useProductQuery'; // Không cần thiết vì API call đ<PERSON><PERSON><PERSON> xử lý ở parent
import { useCustomFields } from '../../hooks/useCustomFieldQuery';

import { NotificationUtil } from '@/shared/utils/notification';
import { FieldValues } from 'react-hook-form';
import { FormRef } from '@/shared/components/common/Form/Form';
import MultiFileUpload, { FileWithMetadata } from '@/modules/data/components/MultiFileUpload';
import { useProductImageUpload } from '@/modules/business/hooks/useProductImageUpload';
import CustomFieldRenderer from '../CustomFieldRenderer';
import SimpleCustomFieldSelector from '../SimpleCustomFieldSelector';
import { useQueryClient } from '@tanstack/react-query';
import { PRODUCT_QUERY_KEYS } from '../../hooks/useProductQuery';

// Interface cho response từ backend khi có ảnh
interface ProductWithImagesResponse {
  id: string;
  name: string;
  price: HasPriceDto | StringPriceDto | null;
  typePrice: string;
  description?: string;
  images: Array<{
    key: string;
    position: number;
    url: string;
  }>;
}

interface ProductWithUploadUrlsResponse {
  id: string;
  name: string;
  price: HasPriceDto | StringPriceDto | null;
  typePrice: string;
  description?: string;
  images: Array<{
    key: string;
    position: number;
    url: string;
  }>;
  uploadUrls: {
    productId: string;
    imagesUploadUrls: Array<{
      url: string;
      key: string;
      index: number;
    }>;
  };
}

interface ProductFormProps {
  onSubmit: (
    values: CreateProductDto
  ) => Promise<
    CreateProductResponse | ProductDto | ProductWithImagesResponse | ProductWithUploadUrlsResponse
  >;
  onCancel: () => void;
  isSubmitting: boolean;
}

// Interface cho trường tùy chỉnh đã chọn
interface SelectedCustomField {
  id: number;
  fieldId: number;
  label: string;
  component: string;
  type: string;
  required: boolean;
  configJson: Record<string, unknown>;
  value: Record<string, unknown>;
}

// Interface cho form values
interface ProductFormValues {
  name: string;
  typePrice: PriceTypeEnum;
  listPrice?: string | number;
  salePrice?: string | number;
  currency?: string;
  priceDescription?: string;
  description?: string;
  tags?: string[];
  shipmentConfig?: {
    lengthCm?: string | number;
    widthCm?: string | number;
    heightCm?: string | number;
    weightGram?: string | number;
  };
  inventory?: {
    warehouseId?: string | number;
    availableQuantity?: string | number;
    sku?: string;
    barcode?: string;
  };
  customFields?: SelectedCustomField[];
  media?: FileWithMetadata[];
  classifications?: ProductVariant[]; // Đổi tên từ variants thành classifications
}

// Interface cho biến thể sản phẩm trong form
interface ProductVariant {
  id: number; // ID tạm thời cho UI
  name: string;
  listPrice: string | number;
  salePrice: string | number;
  currency: string;
  customFields: SelectedCustomField[];
}

/**
 * Form tạo sản phẩm mới
 */
const ProductForm: React.FC<ProductFormProps> = ({ onSubmit, onCancel, isSubmitting }) => {
  const { t } = useTranslation(['business', 'common']);
  // Không sử dụng createProduct ở đây nữa vì đã được gọi từ parent component

  // Schema validation với kiểm tra điều kiện theo loại giá
  const productSchema = z
    .object({
      name: z.string().min(1, 'Tên sản phẩm không được để trống'),
      typePrice: z.nativeEnum(PriceTypeEnum, {
        errorMap: () => ({ message: 'Vui lòng chọn loại giá' }),
      }),
      listPrice: z.union([z.string(), z.number()]).optional(),
      salePrice: z.union([z.string(), z.number()]).optional(),
      currency: z.string().optional(),
      priceDescription: z.string().optional(),
      description: z.string().optional(),
      tags: z.array(z.string()).optional(),
      shipmentConfig: z
        .object({
          lengthCm: z.union([z.string(), z.number()]).optional(),
          widthCm: z.union([z.string(), z.number()]).optional(),
          heightCm: z.union([z.string(), z.number()]).optional(),
          weightGram: z.union([z.string(), z.number()]).optional(),
        })
        .optional(),
      media: z.any().optional(),
      customFields: z.any().optional(),
      classifications: z.any().optional(), // Đổi tên từ variants thành classifications
      inventory: z
        .object({
          warehouseId: z.union([z.string(), z.number()]).optional(),
          availableQuantity: z.union([z.string(), z.number()]).optional(),
          sku: z.string().optional(),
          barcode: z.string().optional(),
        })
        .optional(),
    })
    .superRefine((data, ctx) => {
      // Kiểm tra giá phù hợp với loại giá
      if (data.typePrice === PriceTypeEnum.HAS_PRICE) {
        // Kiểm tra listPrice
        if (!data.listPrice || data.listPrice === '') {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'Vui lòng nhập giá niêm yết',
            path: ['listPrice'],
          });
        } else {
          const listPrice = Number(data.listPrice);
          if (isNaN(listPrice) || listPrice < 0) {
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message: 'Giá niêm yết phải là số >= 0',
              path: ['listPrice'],
            });
          }
        }

        // Kiểm tra salePrice
        if (!data.salePrice || data.salePrice === '') {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'Vui lòng nhập giá bán',
            path: ['salePrice'],
          });
        } else {
          const salePrice = Number(data.salePrice);
          if (isNaN(salePrice) || salePrice < 0) {
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message: 'Giá bán phải là số >= 0',
              path: ['salePrice'],
            });
          }
        }

        // Kiểm tra currency
        if (!data.currency || data.currency.trim() === '') {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'Vui lòng chọn đơn vị tiền tệ',
            path: ['currency'],
          });
        }

        // Kiểm tra giá niêm yết phải lớn hơn giá bán
        if (data.listPrice && data.salePrice && data.listPrice !== '' && data.salePrice !== '') {
          const listPrice = Number(data.listPrice);
          const salePrice = Number(data.salePrice);

          if (!isNaN(listPrice) && !isNaN(salePrice) && listPrice > 0 && salePrice > 0) {
            if (listPrice <= salePrice) {
              ctx.addIssue({
                code: z.ZodIssueCode.custom,
                message: 'Giá niêm yết phải lớn hơn giá bán',
                path: ['listPrice'],
              });
            }
          }
        }
      } else if (data.typePrice === PriceTypeEnum.STRING_PRICE) {
        if (!data.priceDescription || !data.priceDescription.trim()) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'Vui lòng nhập mô tả giá',
            path: ['priceDescription'],
          });
        }
      }
    });

  // State cho tags
  const [tempTags, setTempTags] = useState<string[]>([]);

  // State cho media
  const [mediaFiles, setMediaFiles] = useState<FileWithMetadata[]>([]);
  const [isUploading, setIsUploading] = useState(false);

  // State cho phân loại sản phẩm (đổi tên từ variants)
  const [productClassifications, setProductClassifications] = useState<ProductVariant[]>([]);

  // State cho trường tùy chỉnh của sản phẩm chính
  const [productCustomFields, setProductCustomFields] = useState<SelectedCustomField[]>([]);

  // Form ref
  const formRef = useRef<FormRef<Record<string, unknown>>>(null);

  // Query lấy danh sách trường tùy chỉnh
  useCustomFields();

  // Hook để upload ảnh sản phẩm theo pattern MediaPage
  const { uploadProductImages } = useProductImageUpload();

  // Query client để invalidate cache sau khi upload xong
  const queryClient = useQueryClient();

  // Xử lý khi submit form
  const handleSubmit = async (values: FieldValues) => {
    console.log('🚀 ProductForm handleSubmit called with values:', values);

    // Test đơn giản trước
    if (!values.name || !values.typePrice) {
      console.error('❌ Missing required fields:', {
        name: values.name,
        typePrice: values.typePrice,
      });
      // Không cần hiển thị notification ở đây vì form validation sẽ xử lý
      return;
    }

    try {
      const formValues = values as ProductFormValues;
      setIsUploading(true);

      // Validate dữ liệu trước khi gửi
      console.log('✅ Form values before processing:', formValues);

      // Không còn sử dụng customFields riêng, chỉ sử dụng trong biến thể

      // Chuyển đổi giá trị form thành dữ liệu API (đã sync với Backend)
      let priceData;
      try {
        priceData = getPriceData(formValues);
      } catch (priceError) {
        console.error('❌ Price validation error:', priceError);
        // Không cần hiển thị notification ở đây vì form validation sẽ xử lý
        setIsUploading(false);
        return;
      }

      const productData: CreateProductDto = {
        name: formValues.name,
        typePrice: formValues.typePrice,
        price: priceData,
        description: formValues.description || undefined,
        tags: formValues.tags && formValues.tags.length > 0 ? formValues.tags : undefined,
        shipmentConfig: getShipmentConfig(formValues),
        imagesMediaTypes:
          mediaFiles.length > 0 ? mediaFiles.map(file => file.file.type) : undefined,
        customFields:
          productCustomFields.length > 0
            ? productCustomFields.map(field => ({
                customFieldId: field.fieldId,
                value: {
                  value: field.value.value,
                },
              }))
            : undefined,
        classifications:
          productClassifications.length > 0
            ? productClassifications.map(variant => ({
                type: variant.name,
                price: {
                  listPrice: Number(variant.listPrice) || 0,
                  salePrice: Number(variant.salePrice) || 0,
                  currency: variant.currency,
                },
                customFields: variant.customFields.map(field => ({
                  customFieldId: field.fieldId,
                  value: {
                    value: field.value.value,
                  },
                })),
              }))
            : undefined,
        inventory: getInventoryData(formValues),
      };

      console.log('📤 Final product data to be sent to API:', JSON.stringify(productData, null, 2));

      // Gọi callback onSubmit để parent component xử lý API call và nhận response
      const response = await onSubmit(productData);

      console.log('✅ Product created successfully:', response);

      // Upload media nếu có và API trả về images với upload URLs
      if (mediaFiles.length > 0) {
        try {
          // Kiểm tra xem response có uploadUrls.imagesUploadUrls không
          const hasUploadUrls =
            response &&
            typeof response === 'object' &&
            'uploadUrls' in response &&
            response.uploadUrls &&
            typeof response.uploadUrls === 'object' &&
            'imagesUploadUrls' in response.uploadUrls &&
            Array.isArray(response.uploadUrls.imagesUploadUrls);

          if (hasUploadUrls) {
            const uploadUrls = response.uploadUrls.imagesUploadUrls;

            if (uploadUrls.length > 0) {
              console.log('🚀 Starting image upload with TaskQueue...');
              console.log('📁 Media files:', mediaFiles.length);
              console.log('🔗 Upload URLs from backend:', uploadUrls.length);
              console.log('📋 Backend response uploadUrls:', uploadUrls);

              // Tạo mapping giữa media files và upload URLs từ backend
              const uploadTasks = mediaFiles.slice(0, uploadUrls.length).map((fileData, index) => {
                const uploadInfo = uploadUrls[index];
                return {
                  file: fileData.file,
                  uploadUrl: uploadInfo.url,
                  key: uploadInfo.key,
                  index: uploadInfo.index,
                };
              });

              console.log('📤 Upload tasks:', uploadTasks);

              // Upload tất cả ảnh cùng lúc với Promise.all
              console.log(`🚀 Uploading ${uploadTasks.length} files simultaneously...`);

              // Tạo array các file và URLs để upload cùng lúc
              const filesToUpload = uploadTasks.map((task, index) => ({
                file: task.file,
                id: `${Date.now()}_${index}`,
              }));
              const urlsToUpload = uploadTasks.map(task => task.uploadUrl);

              // Upload tất cả ảnh cùng lúc, skip cache invalidation trong hook
              await uploadProductImages(filesToUpload, urlsToUpload, {
                skipCacheInvalidation: true,
              });

              console.log('✅ All product images uploaded successfully');

              // Invalidate cache để refresh danh sách sản phẩm một lần duy nhất
              queryClient.invalidateQueries({ queryKey: PRODUCT_QUERY_KEYS.lists() });

              // Hiển thị notification riêng cho upload media thành công
              NotificationUtil.success({
                message: t(
                  'business:product.mediaUploadSuccess',
                  'Tải lên ảnh sản phẩm thành công'
                ),
                duration: 3000,
              });
            } else {
              console.warn('⚠️ Upload URLs array is empty');
              NotificationUtil.warning({
                message: t(
                  'business:product.mediaUploadWarning',
                  'Sản phẩm đã được tạo nhưng không thể tải lên ảnh'
                ),
                duration: 5000,
              });
            }
          } else {
            console.warn('⚠️ Media files exist but no upload URLs provided from backend');
            console.log('📝 Backend response structure:', response);

            NotificationUtil.warning({
              message: t(
                'business:product.mediaUploadWarning',
                'Sản phẩm đã được tạo nhưng không thể tải lên ảnh'
              ),
              duration: 5000,
            });
          }
        } catch (uploadError) {
          console.error('❌ Error uploading product images:', uploadError);
          NotificationUtil.warning({
            message: t(
              'business:product.mediaUploadError',
              'Có lỗi xảy ra khi tải lên ảnh sản phẩm'
            ),
            duration: 5000,
          });
        }
      }

      setIsUploading(false);
    } catch (error) {
      console.error('Error in ProductForm handleSubmit:', error);
      setIsUploading(false);

      // Không cần hiển thị notification ở đây nữa vì hook useCreateProduct đã xử lý
      // Chỉ log error để debug
      if (error && typeof error === 'object' && 'issues' in error) {
        console.error('Validation errors:', error);
      }

      // Re-throw error để parent component có thể xử lý nếu cần
      throw error;
    }
  };

  // Hàm lấy dữ liệu giá dựa trên loại giá
  const getPriceData = (values: ProductFormValues): HasPriceDto | StringPriceDto | null => {
    if (values.typePrice === PriceTypeEnum.HAS_PRICE) {
      // Kiểm tra đầy đủ các trường bắt buộc
      if (!values.listPrice || values.listPrice === '') {
        throw new Error('Vui lòng nhập giá niêm yết');
      }

      if (!values.salePrice || values.salePrice === '') {
        throw new Error('Vui lòng nhập giá bán');
      }

      if (!values.currency || values.currency.trim() === '') {
        throw new Error('Vui lòng chọn đơn vị tiền tệ');
      }

      const listPrice = Number(values.listPrice);
      const salePrice = Number(values.salePrice);

      if (isNaN(listPrice) || listPrice < 0) {
        throw new Error('Giá niêm yết phải là số >= 0');
      }

      if (isNaN(salePrice) || salePrice < 0) {
        throw new Error('Giá bán phải là số >= 0');
      }

      // Kiểm tra giá niêm yết phải lớn hơn giá bán
      if (listPrice <= salePrice) {
        throw new Error('Giá niêm yết phải lớn hơn giá bán');
      }

      return {
        listPrice,
        salePrice,
        currency: values.currency.trim(),
      };
    } else if (values.typePrice === PriceTypeEnum.STRING_PRICE) {
      if (!values.priceDescription || !values.priceDescription.trim()) {
        throw new Error('Vui lòng nhập mô tả giá');
      }

      return {
        priceDescription: values.priceDescription.trim(),
      };
    } else if (values.typePrice === PriceTypeEnum.NO_PRICE) {
      return null;
    }

    throw new Error('Loại giá không hợp lệ');
  };

  // Hàm lấy dữ liệu cấu hình vận chuyển
  const getShipmentConfig = (values: ProductFormValues) => {
    if (!values.shipmentConfig) return undefined;

    const config = values.shipmentConfig;
    const hasAnyValue = config.lengthCm || config.widthCm || config.heightCm || config.weightGram;

    if (!hasAnyValue) return undefined;

    return {
      lengthCm: Number(config.lengthCm) || undefined,
      widthCm: Number(config.widthCm) || undefined,
      heightCm: Number(config.heightCm) || undefined,
      weightGram: Number(config.weightGram) || undefined,
    };
  };

  // Hàm lấy dữ liệu tồn kho
  const getInventoryData = (values: ProductFormValues) => {
    console.log('🔍 getInventoryData called with values.inventory:', values.inventory);

    if (!values.inventory) {
      console.log('❌ No inventory data found');
      return undefined;
    }

    const inventory = values.inventory;

    // Kiểm tra xem có warehouseId không (bắt buộc)
    if (!inventory.warehouseId) {
      console.log('❌ No warehouseId found, inventory will be undefined');
      return undefined;
    }

    const inventoryData = {
      warehouseId: Number(inventory.warehouseId),
      availableQuantity: inventory.availableQuantity ? Number(inventory.availableQuantity) : 0,
      sku: inventory.sku || undefined,
      barcode: inventory.barcode || undefined,
    };

    console.log('✅ Generated inventory data:', inventoryData);
    return inventoryData;
  };

  // Thêm/xóa trường tùy chỉnh vào sản phẩm chính
  const handleToggleCustomFieldToProduct = useCallback(
    (fieldId: number, fieldData?: Record<string, unknown>) => {
      setProductCustomFields(prev => {
        // Kiểm tra xem trường đã tồn tại chưa
        const existingFieldIndex = prev.findIndex(field => field.fieldId === fieldId);

        if (existingFieldIndex !== -1) {
          // Nếu đã tồn tại, xóa nó (bỏ chọn)
          return prev.filter((_, index) => index !== existingFieldIndex);
        }

        // Thêm trường mới với thông tin đầy đủ
        const newField: SelectedCustomField = {
          id: Date.now(), // ID tạm thời
          fieldId,
          label: (fieldData?.label as string) || `Field ${fieldId}`,
          component: (fieldData?.component as string) || (fieldData?.type as string) || 'text',
          type: (fieldData?.type as string) || 'text',
          required: (fieldData?.required as boolean) || false,
          configJson: (fieldData?.configJson as Record<string, unknown>) || {},
          value: { value: '' }, // Giá trị mặc định
        };

        return [...prev, newField];
      });
    },
    []
  );

  // Xóa trường tùy chỉnh khỏi sản phẩm chính
  const handleRemoveCustomFieldFromProduct = useCallback((customFieldId: number) => {
    setProductCustomFields(prev => prev.filter(field => field.id !== customFieldId));
  }, []);

  // Cập nhật giá trị trường tùy chỉnh trong sản phẩm chính
  const handleUpdateCustomFieldInProduct = useCallback((customFieldId: number, value: string) => {
    setProductCustomFields(prev =>
      prev.map(field => {
        if (field.id === customFieldId) {
          return {
            ...field,
            value: { value },
          };
        }
        return field;
      })
    );
  }, []);

  // Thêm phân loại mới (đổi tên từ variant)
  const handleAddVariant = useCallback(() => {
    const newVariant: ProductVariant = {
      id: Date.now(), // ID tạm thời cho UI
      name: '',
      listPrice: '',
      salePrice: '',
      currency: 'VND',
      customFields: [],
    };

    setProductClassifications(prev => [...prev, newVariant]);
    // Không cần setValue cho classifications vì chúng ta quản lý riêng trong state
  }, []);

  // Xóa phân loại (đổi tên từ variant)
  const handleRemoveVariant = useCallback((variantId: number) => {
    setProductClassifications(prev => prev.filter(variant => variant.id !== variantId));
    // Không cần setValue cho classifications vì chúng ta quản lý riêng trong state
  }, []);

  // Cập nhật phân loại (đổi tên từ variant)
  const handleUpdateVariant = useCallback(
    (variantId: number, field: string, value: string | number) => {
      setProductClassifications(prev =>
        prev.map(variant => {
          if (variant.id === variantId) {
            return { ...variant, [field]: value };
          }
          return variant;
        })
      );
      // Không cần setValue cho classifications vì chúng ta quản lý riêng trong state
    },
    []
  );

  // Thêm/xóa trường tùy chỉnh vào phân loại (đổi tên từ variant)
  const handleToggleCustomFieldToVariant = useCallback(
    (variantId: number, fieldId: number, fieldData?: Record<string, unknown>) => {
      setProductClassifications(prev =>
        prev.map(variant => {
          if (variant.id === variantId) {
            // Kiểm tra xem trường đã tồn tại trong phân loại chưa
            const existingFieldIndex = variant.customFields.findIndex(
              field => field.fieldId === fieldId
            );

            if (existingFieldIndex !== -1) {
              // Nếu đã tồn tại, xóa nó (bỏ chọn)
              return {
                ...variant,
                customFields: variant.customFields.filter(
                  (_, index) => index !== existingFieldIndex
                ),
              };
            }

            // Thêm trường mới vào phân loại với thông tin đầy đủ
            return {
              ...variant,
              customFields: [
                ...variant.customFields,
                {
                  id: Date.now(), // ID tạm thời
                  fieldId,
                  label: (fieldData?.label as string) || `Field ${fieldId}`,
                  component: (fieldData?.component as string) || (fieldData?.type as string) || 'text',
                  type: (fieldData?.type as string) || 'text',
                  required: (fieldData?.required as boolean) || false,
                  configJson: (fieldData?.configJson as Record<string, unknown>) || {},
                  value: { value: '' }, // Giá trị mặc định
                },
              ],
            };
          }
          return variant;
        })
      );
    },
    []
  );

  // Xóa trường tùy chỉnh khỏi phân loại (đổi tên từ variant)
  const handleRemoveCustomFieldFromVariant = useCallback(
    (variantId: number, customFieldId: number) => {
      setProductClassifications(prev =>
        prev.map(variant => {
          if (variant.id === variantId) {
            return {
              ...variant,
              customFields: variant.customFields.filter(field => field.id !== customFieldId),
            };
          }
          return variant;
        })
      );
      // Không cần setValue cho classifications vì chúng ta quản lý riêng trong state
    },
    []
  );

  // Cập nhật giá trị trường tùy chỉnh trong phân loại (đổi tên từ variant)
  const handleUpdateCustomFieldInVariant = useCallback(
    (variantId: number, customFieldId: number, value: string) => {
      setProductClassifications(prev =>
        prev.map(variant => {
          if (variant.id === variantId) {
            return {
              ...variant,
              customFields: variant.customFields.map(field => {
                if (field.id === customFieldId) {
                  return {
                    ...field,
                    value: { value },
                  };
                }
                return field;
              }),
            };
          }
          return variant;
        })
      );
      // Không cần setValue cho classifications vì chúng ta quản lý riêng trong state
    },
    []
  );

  // Giá trị mặc định cho form - sử dụng useMemo để tránh re-create
  const defaultValues = useMemo(
    () => ({
      name: '',
      typePrice: PriceTypeEnum.HAS_PRICE,
      listPrice: '',
      salePrice: '',
      currency: 'VND',
      priceDescription: '',
      description: '',
      tags: [],
      shipmentConfig: {
        lengthCm: '',
        widthCm: '',
        heightCm: '',
        weightGram: '',
      },
      inventory: {
        warehouseId: '',
        availableQuantity: '',
        sku: '',
        barcode: '',
      },
      customFields: [],
      media: [],
      classifications: [], // Đổi tên từ variants
    }),
    []
  );

  return (
    <FormMultiWrapper title={t('business:product.form.createTitle')}>
      <Form
        ref={formRef}
        schema={productSchema}
        onSubmit={handleSubmit}
        onError={errors => {
          console.error('🔥 Form validation errors:', errors);

          // Log chi tiết từng field error
          Object.keys(errors).forEach(field => {
            console.error(`❌ Field "${field}":`, errors[field]);
            if (errors[field]?.message) {
              console.error(`   Message: ${errors[field].message}`);
            }
            if (errors[field] && typeof errors[field] === 'object' && 'type' in errors[field]) {
              console.error(`   Type: ${(errors[field] as { type: string }).type}`);
            }
          });

          // Hiển thị error đầu tiên để user biết
          const firstError = Object.values(errors)[0];
          const errorMessage = firstError?.message || 'Vui lòng kiểm tra lại thông tin đã nhập';

          NotificationUtil.error({
            message: errorMessage,
            duration: 5000,
          });
        }}
        defaultValues={defaultValues}
        submitOnEnter={false}
        className="space-y-4"
      >
        {/* 1. Thông tin chung */}
        <CollapsibleCard
          title={
            <Typography variant="h6" className="font-medium">
              {t('business:product.form.sections.generalInfo', '1. Thông tin chung')}
            </Typography>
          }
          defaultOpen={true}
          className="mb-4"
        >
          <div className="space-y-4">
            <FormItem name="name" label={t('business:product.name')} required>
              <Input fullWidth placeholder={t('business:product.form.namePlaceholder')} />
            </FormItem>

            <FormItem name="description" label={t('business:product.form.description')}>
              <Textarea
                fullWidth
                rows={4}
                placeholder={t('business:product.form.descriptionPlaceholder')}
              />
            </FormItem>

            <FormItem name="tags" label={t('business:product.tags')}>
              <Controller
                name="tags"
                render={({ field }) => (
                  <div className="space-y-2">
                    <Input
                      fullWidth
                      placeholder={t('business:product.form.tagsPlaceholder')}
                      onKeyDown={e => {
                        if (e.key === 'Enter' && e.currentTarget.value.trim()) {
                          e.preventDefault();

                          // Lấy tag mới
                          const newTag = e.currentTarget.value.trim();

                          // Thêm tag mới nếu chưa tồn tại
                          if (!tempTags.includes(newTag)) {
                            const newTags = [...tempTags, newTag];
                            setTempTags(newTags);
                            field.onChange(newTags); // Đồng bộ với form
                          }

                          e.currentTarget.value = '';
                        }
                      }}
                    />
                    <div className="flex flex-wrap gap-1 mt-2">
                      {tempTags.map((tag, tagIndex) => (
                        <Chip
                          key={`tag-${tagIndex}-${tag}`}
                          size="sm"
                          closable
                          onClose={() => {
                            const newTags = tempTags.filter(t => t !== tag);
                            setTempTags(newTags);
                            field.onChange(newTags); // Đồng bộ với form
                          }}
                        >
                          {tag}
                        </Chip>
                      ))}
                    </div>
                  </div>
                )}
              />
            </FormItem>
          </div>
        </CollapsibleCard>

        {/* 2. Giá sản phẩm */}
        <CollapsibleCard
          title={
            <Typography variant="h6" className="font-medium">
              {t('business:product.form.sections.pricing', '2. Giá sản phẩm')}
            </Typography>
          }
          defaultOpen={true}
          className="mb-4"
        >
          <div className="space-y-4">
            <FormItem name="typePrice" label={t('business:product.priceType.title')} required>
              <Select
                fullWidth
                options={[
                  {
                    value: PriceTypeEnum.HAS_PRICE,
                    label: t('business:product.priceType.hasPrice'),
                  },
                  {
                    value: PriceTypeEnum.STRING_PRICE,
                    label: t('business:product.priceType.stringPrice'),
                  },
                  { value: PriceTypeEnum.NO_PRICE, label: t('business:product.priceType.noPrice') },
                ]}
              />
            </FormItem>

            {/* Hiển thị các trường giá dựa trên loại giá */}
            <ConditionalField
              condition={{
                field: 'typePrice',
                type: ConditionType.EQUALS,
                value: PriceTypeEnum.HAS_PRICE,
              }}
            >
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormItem name="listPrice" label={t('business:product.listPrice')} required>
                  <Input fullWidth type="number" min="0" placeholder="Nhập giá niêm yết" />
                </FormItem>
                <FormItem name="salePrice" label={t('business:product.salePrice')} required>
                  <Input fullWidth type="number" min="0" placeholder="Nhập giá bán" />
                </FormItem>
                <FormItem name="currency" label={t('business:product.currency')} required>
                  <Controller
                    name="currency"
                    render={({ field }) => (
                      <Select
                        fullWidth
                        value={field.value || 'VND'}
                        onChange={value => field.onChange(value)}
                        options={[
                          { value: 'VND', label: 'VND' },
                          { value: 'USD', label: 'USD' },
                          { value: 'EUR', label: 'EUR' },
                        ]}
                      />
                    )}
                  />
                </FormItem>
              </div>
            </ConditionalField>

            <ConditionalField
              condition={{
                field: 'typePrice',
                type: ConditionType.EQUALS,
                value: PriceTypeEnum.STRING_PRICE,
              }}
            >
              <FormItem
                name="priceDescription"
                label={t('business:product.priceDescription')}
                required
              >
                <Input
                  fullWidth
                  placeholder={t('business:product.form.priceDescriptionPlaceholder')}
                />
              </FormItem>
            </ConditionalField>
          </div>
        </CollapsibleCard>

        {/* 3. Hình ảnh sản phẩm */}
        <CollapsibleCard
          title={
            <Typography variant="h6" className="font-medium">
              {t('business:product.form.sections.images', '3. Hình ảnh sản phẩm')}
            </Typography>
          }
          defaultOpen={false}
          className="mb-4"
        >
          <FormItem name="media" label={t('business:product.form.media')}>
            <MultiFileUpload
              mediaOnly={true}
              accept="image/*,video/*"
              placeholder={t(
                'business:product.form.mediaPlaceholder',
                'Kéo thả hoặc click để tải lên ảnh/video'
              )}
              onChange={files => {
                setMediaFiles(files);
                // Không gọi setValue để tránh reset form
              }}
              value={mediaFiles}
            />
          </FormItem>
        </CollapsibleCard>

        {/* 4. Quản lý tồn kho */}
        <CollapsibleCard
          title={
            <Typography variant="h6" className="font-medium">
              {t('business:product.form.sections.inventory', '4. Quản lý tồn kho')}
            </Typography>
          }
          defaultOpen={false}
          className="mb-4"
        >
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormItem
                name="inventory.warehouseId"
                label={t('business:product.form.inventory.warehouse', 'Kho')}
                required
              >
                <AsyncSelectWithPagination
                  fullWidth
                  placeholder={t('business:product.form.inventory.warehousePlaceholder', 'Chọn kho')}
                  loadOptions={async ({ search, page, limit }: {
                    search?: string;
                    page?: number;
                    limit?: number;
                  }) => {
                    // Gọi API thực tế để lấy danh sách kho
                    const response = await WarehouseService.getWarehousesForSelect({
                      search,
                      page: page || 1,
                      limit: limit || 20,
                    });

                    return response;
                  }}
                  searchOnEnter={true}
                />
              </FormItem>

              <FormItem
                name="inventory.availableQuantity"
                label={t('business:product.form.inventory.availableQuantity', 'Số lượng có sẵn')}
              >
                <Input
                  fullWidth
                  type="number"
                  min="0"
                  placeholder="0"
                />
              </FormItem>

              <FormItem
                name="inventory.sku"
                label={t('business:product.form.inventory.sku', 'SKU')}
              >
                <Input
                  fullWidth
                  placeholder="SKU-001"
                />
              </FormItem>

              <FormItem
                name="inventory.barcode"
                label={t('business:product.form.inventory.barcode', 'Barcode')}
              >
                <Input
                  fullWidth
                  placeholder="1234567890123"
                />
              </FormItem>
            </div>
          </div>
        </CollapsibleCard>

        {/* 5. Vận chuyển */}
        <CollapsibleCard
          title={
            <Typography variant="h6" className="font-medium">
              {t('business:product.form.sections.shipping', '5. Vận chuyển')}
            </Typography>
          }
          defaultOpen={false}
          className="mb-4"
        >
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormItem
                name="shipmentConfig.widthCm"
                label={t('business:product.form.shipmentConfig.widthCm', 'Chiều rộng (cm)')}
              >
                <Input fullWidth type="number" min="0" placeholder="0" />
              </FormItem>
              <FormItem
                name="shipmentConfig.heightCm"
                label={t('business:product.form.shipmentConfig.heightCm', 'Chiều cao (cm)')}
              >
                <Input fullWidth type="number" min="0" placeholder="0" />
              </FormItem>
              <FormItem
                name="shipmentConfig.lengthCm"
                label={t('business:product.form.shipmentConfig.lengthCm', 'Chiều dài (cm)')}
              >
                <Input fullWidth type="number" min="0" placeholder="0" />
              </FormItem>
              <FormItem
                name="shipmentConfig.weightGram"
                label={t('business:product.form.shipmentConfig.weightGram', 'Khối lượng (gram)')}
              >
                <Input fullWidth type="number" min="0" placeholder="0" />
              </FormItem>
            </div>
          </div>
        </CollapsibleCard>

        {/* 6. Mẫu mã */}
        <CollapsibleCard
          title={
            <div className="flex items-center justify-between w-full">
              <Typography variant="h6" className="font-medium">
                {t('business:product.form.sections.variants', '6. Mẫu mã')}
              </Typography>
              <div onClick={(e) => {
                e.preventDefault();
                e.stopPropagation(); // Ngăn không cho toggle card
              }}>
                <IconCard
                  icon="plus"
                  variant="primary"
                  size="sm"
                  title={t('business:product.form.variants.addVariant', 'Thêm biến thể')}
                  onClick={handleAddVariant}
                />
              </div>
            </div>
          }
          defaultOpen={false}
          className="mb-4"
        >
          {/* Danh sách phân loại (đổi tên từ biến thể) */}
          {productClassifications.length > 0 ? (
            <div className="space-y-6">
              {productClassifications.map((variant, index) => (
                <div key={variant.id} className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-4">
                    <Typography variant="subtitle1" className="font-medium">
                      {t('business:product.form.variants.variant', 'Biến thể')} #{index + 1}
                    </Typography>
                    <IconCard
                      icon="trash"
                      variant="danger"
                      size="sm"
                      title={t('common:delete', 'Xóa')}
                      onClick={() => handleRemoveVariant(variant.id)}
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <FormItem label={t('business:product.form.variants.name', 'Tên biến thể')}>
                      <Input
                        fullWidth
                        value={variant.name}
                        onChange={e => handleUpdateVariant(variant.id, 'name', e.target.value)}
                        placeholder={t(
                          'business:product.form.variants.namePlaceholder',
                          'Nhập tên biến thể'
                        )}
                      />
                    </FormItem>

                    <FormItem
                      label={t('business:product.form.variants.currency', 'Đơn vị tiền tệ')}
                    >
                      <Select
                        fullWidth
                        value={variant.currency}
                        onChange={val => handleUpdateVariant(variant.id, 'currency', val as string)}
                        options={[
                          { value: 'VND', label: 'VND' },
                          { value: 'USD', label: 'USD' },
                          { value: 'EUR', label: 'EUR' },
                        ]}
                      />
                    </FormItem>

                    <FormItem label={t('business:product.form.variants.listPrice', 'Giá niêm yết')}>
                      <Input
                        fullWidth
                        type="number"
                        min="0"
                        value={variant.listPrice}
                        onChange={e => handleUpdateVariant(variant.id, 'listPrice', e.target.value)}
                        placeholder="0"
                      />
                    </FormItem>

                    <FormItem label={t('business:product.form.variants.salePrice', 'Giá bán')}>
                      <Input
                        fullWidth
                        type="number"
                        min="0"
                        value={variant.salePrice}
                        onChange={e => handleUpdateVariant(variant.id, 'salePrice', e.target.value)}
                        placeholder="0"
                      />
                    </FormItem>
                  </div>

                  {/* Trường tùy chỉnh cho biến thể */}
                  <div className="mt-4">
                    <div className="flex items-center justify-between mb-2">
                      <Typography variant="subtitle2">
                        {t('business:product.form.variants.customFields', 'Thuộc tính biến thể')}
                      </Typography>
                    </div>

                    <SimpleCustomFieldSelector
                      onFieldSelect={fieldData => {
                        handleToggleCustomFieldToVariant(
                          variant.id,
                          fieldData.id,
                          fieldData as unknown as Record<string, unknown>
                        );
                      }}
                      selectedFieldIds={variant.customFields.map(f => f.fieldId)}
                      placeholder={t(
                        'business:product.form.variants.searchCustomField',
                        'Nhập từ khóa và nhấn Enter để tìm thuộc tính...'
                      )}
                    />

                    {variant.customFields.length > 0 && (
                      <div className="space-y-3">
                        {variant.customFields.map(field => (
                          <CustomFieldRenderer
                            key={field.id}
                            field={field}
                            value={(field.value.value as string) || ''}
                            onChange={value =>
                              handleUpdateCustomFieldInVariant(
                                variant.id,
                                field.id,
                                value as string
                              )
                            }
                            onRemove={() =>
                              handleRemoveCustomFieldFromVariant(variant.id, field.id)
                            }
                          />
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center p-6 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <Typography variant="body2" className="text-gray-500 dark:text-gray-400">
                {t(
                  'business:product.form.variants.noVariants',
                  'Chưa có biến thể nào. Nhấn "Thêm biến thể" để bắt đầu.'
                )}
              </Typography>
            </div>
          )}
        </CollapsibleCard>

        {/* 7. Trường tùy chỉnh */}
        <CollapsibleCard
          title={
            <Typography variant="h6" className="font-medium">
              {t('business:product.form.sections.customFields', '7. Trường tùy chỉnh')}
            </Typography>
          }
          defaultOpen={false}
          className="mb-4"
        >
          <div className="space-y-4">
            <SimpleCustomFieldSelector
              onFieldSelect={fieldData => {
                handleToggleCustomFieldToProduct(
                  fieldData.id,
                  fieldData as unknown as Record<string, unknown>
                );
              }}
              selectedFieldIds={productCustomFields.map(f => f.fieldId)}
              placeholder={t(
                'business:product.form.customFields.searchPlaceholder',
                'Nhập từ khóa và nhấn Enter để tìm trường tùy chỉnh...'
              )}
            />

            {productCustomFields.length > 0 && (
              <div className="space-y-3">
                {productCustomFields.map(field => (
                  <CustomFieldRenderer
                    key={field.id}
                    field={field}
                    value={(field.value.value as string) || ''}
                    onChange={value => handleUpdateCustomFieldInProduct(field.id, value as string)}
                    onRemove={() => handleRemoveCustomFieldFromProduct(field.id)}
                  />
                ))}
              </div>
            )}
          </div>
        </CollapsibleCard>

        <div className="flex flex-col sm:flex-row sm:justify-end gap-2 pt-4">
          <IconCard
            icon="x"
            variant="secondary"
            size="md"
            title={t('common:cancel')}
            onClick={onCancel}
            disabled={isSubmitting || isUploading}
          />
          <IconCard
            icon="save"
            variant="primary"
            size="md"
            title={isUploading ? t('business:product.uploading') : t('common:save')}
            onClick={() => {
              // Trigger form submit programmatically
              formRef.current?.submit();
            }}
            disabled={isSubmitting || isUploading}
            isLoading={isSubmitting || isUploading}
          />
        </div>
      </Form>
    </FormMultiWrapper>
  );
};

export default ProductForm;
