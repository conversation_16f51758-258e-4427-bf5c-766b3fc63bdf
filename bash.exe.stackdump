Stack trace:
Frame         Function      Args
0007FFFF9C30  00021006118E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFF8B30) msys-2.0.dll+0x2118E
0007FFFF9C30  0002100469BA (000000000000, 000000000000, 000000000000, 000000000004) msys-2.0.dll+0x69BA
0007FFFF9C30  0002100469F2 (00021028DF99, 0007FFFF9AE8, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFF9C30  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFF9C30  00021006A545 (0007FFFF9C40, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0001004F94B7  00021006B9A5 (0007FFFF9C40, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFEDDCD0000 ntdll.dll
7FFEDBF10000 KERNEL32.DLL
7FFEDB3E0000 KERNELBASE.dll
7FFEDCC20000 USER32.dll
7FFEDB930000 win32u.dll
7FFEDBD10000 GDI32.dll
7FFEDB2B0000 gdi32full.dll
7FFEDAE40000 msvcp_win.dll
7FFEDAF60000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFEDCA50000 advapi32.dll
7FFEDC880000 msvcrt.dll
7FFEDBB00000 sechost.dll
7FFEDB080000 bcrypt.dll
7FFEDC930000 RPCRT4.dll
7FFEDA4F0000 CRYPTBASE.DLL
7FFEDADC0000 bcryptPrimitives.dll
7FFEDCF20000 IMM32.DLL
