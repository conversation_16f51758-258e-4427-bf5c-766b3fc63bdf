import { apiClient as apiRequest } from '@/shared/api/';
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';
import { PaginatedResult } from '@/shared/dto/response/paginated-result.dto';

/**
 * Enum cho trạng thái đơn hàng
 */
export enum OrderStatus {
  PENDING = 'PENDING',
  PROCESSING = 'PROCESSING',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED',
  REFUNDED = 'REFUNDED',
}

/**
 * Enum cho phương thức thanh toán
 */
export enum PaymentMethod {
  CASH = 'CASH',
  CREDIT_CARD = 'CREDIT_CARD',
  BANK_TRANSFER = 'BANK_TRANSFER',
  DIGITAL_WALLET = 'DIGITAL_WALLET',
}

/**
 * Interface cho thông tin khách hàng
 */
export interface CustomerInfo {
  id: number;
  name: string;
  email: string;
  phone: string;
  address?: string;
}

/**
 * Interface cho sản phẩm trong đơn hàng
 */
export interface OrderItem {
  id: number;
  productId: number;
  productName: string;
  quantity: number;
  price: number;
  totalPrice: number;
}

/**
 * Interface cho đơn hàng
 */
export interface Order {
  id: number;
  orderNumber: string;
  customerId: number;
  customerInfo: CustomerInfo;
  items: OrderItem[];
  totalAmount: number;
  status: OrderStatus;
  paymentMethod: PaymentMethod;
  paymentStatus: 'PAID' | 'UNPAID' | 'PARTIALLY_PAID';
  createdAt: number;
  updatedAt: number;
  notes?: string;
}

/**
 * Interface cho danh sách đơn hàng (dựa trên API thật)
 */
export interface OrderListItem {
  id: string;
  userConvertCustomer: {
    id: string;
    avatar: string;
    name: string;
    email: {
      primary: string;
      secondary: string;
    };
    phone: string;
    platform: string | null;
    timezone: string | null;
    createdAt: string;
    updatedAt: string;
    address: string;
    metadata: Record<string, unknown>;
  };
  billInfo: {
    total: number;
    subtotal: number;
    shippingFee: number;
    paymentMethod: string;
    paymentStatus: string;
    selectedCarrier: string;
    shippingServiceType: string;
  };
  shippingStatus: string;
  createdAt: string;
  source: string;
  orderStatus: string;
}

/**
 * Interface cho danh sách đơn hàng đã được transform
 */
export interface TransformedOrderListItem {
  id: string;
  orderNumber: string;
  customerName: string;
  totalAmount: number;
  status: OrderStatus;
  paymentStatus: 'PAID' | 'UNPAID' | 'PARTIALLY_PAID';
  shippingStatus: string;
  createdAt: number;
  source: string;
}

/**
 * Interface cho tham số truy vấn đơn hàng
 */
export interface OrderQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  status?: OrderStatus;
  paymentStatus?: 'PAID' | 'UNPAID' | 'PARTIALLY_PAID';
  fromDate?: string;
  toDate?: string;
  sortBy?: string;
  sortDirection?: 'ASC' | 'DESC';
}

/**
 * Interface cho dữ liệu tạo đơn hàng
 */
export interface CreateOrderData {
  customerId: number;
  items: Array<{
    productId: number;
    quantity: number;
    price: number;
  }>;
  paymentMethod: PaymentMethod;
  notes?: string;
}

/**
 * Interface cho dữ liệu cập nhật đơn hàng
 */
export interface UpdateOrderData {
  status?: OrderStatus;
  paymentStatus?: 'PAID' | 'UNPAID' | 'PARTIALLY_PAID';
  notes?: string;
}

/**
 * Hàm transform dữ liệu từ API thành format hiển thị
 */
export const transformOrderListItem = (item: OrderListItem): TransformedOrderListItem => {
  // Map payment status từ API
  const getPaymentStatus = (paymentStatus: string): 'PAID' | 'UNPAID' | 'PARTIALLY_PAID' => {
    switch (paymentStatus.toUpperCase()) {
      case 'PAID':
      case 'COMPLETED':
        return 'PAID';
      case 'PENDING':
      case 'UNPAID':
        return 'UNPAID';
      case 'PARTIAL':
      case 'PARTIALLY_PAID':
        return 'PARTIALLY_PAID';
      default:
        return 'UNPAID';
    }
  };

  // Map order status từ API
  const getOrderStatus = (status: string): OrderStatus => {
    switch (status.toUpperCase()) {
      case 'PENDING':
        return OrderStatus.PENDING;
      case 'PROCESSING':
        return OrderStatus.PROCESSING;
      case 'COMPLETED':
        return OrderStatus.COMPLETED;
      case 'CANCELLED':
        return OrderStatus.CANCELLED;
      case 'REFUNDED':
        return OrderStatus.REFUNDED;
      default:
        return OrderStatus.PENDING;
    }
  };

  return {
    id: item.id,
    orderNumber: `#${item.id}`,
    customerName: item.userConvertCustomer.name,
    totalAmount: item.billInfo.total,
    status: getOrderStatus(item.orderStatus),
    paymentStatus: getPaymentStatus(item.billInfo.paymentStatus),
    shippingStatus: item.shippingStatus,
    createdAt: parseInt(item.createdAt),
    source: item.source,
  };
};

/**
 * Service xử lý API liên quan đến đơn hàng
 */
export const OrderService = {
  /**
   * Lấy danh sách đơn hàng
   * @param params Tham số truy vấn
   * @returns Danh sách đơn hàng với phân trang
   */
  getOrders: async (params?: OrderQueryParams): Promise<ApiResponseDto<PaginatedResult<OrderListItem>>> => {
    return apiRequest.get('/user/orders', { params });
  },

  /**
   * Lấy chi tiết đơn hàng theo ID
   * @param id ID của đơn hàng
   * @returns Chi tiết đơn hàng
   */
  getOrderById: async (id: number): Promise<ApiResponseDto<Order>> => {
    return apiRequest.get(`/user/orders/${id}`);
  },

  /**
   * Tạo đơn hàng mới
   * @param data Dữ liệu tạo đơn hàng
   * @returns Thông tin đơn hàng đã tạo
   */
  createOrder: async (data: CreateOrderData): Promise<ApiResponseDto<Order>> => {
    return apiRequest.post('/user/orders', data);
  },

  /**
   * Cập nhật đơn hàng
   * @param id ID của đơn hàng
   * @param data Dữ liệu cập nhật đơn hàng
   * @returns Thông tin đơn hàng đã cập nhật
   */
  updateOrder: async (id: number, data: UpdateOrderData): Promise<ApiResponseDto<Order>> => {
    return apiRequest.put(`/user/orders/${id}`, data);
  },

  /**
   * Xóa đơn hàng
   * @param id ID của đơn hàng
   * @returns Thông báo xóa thành công
   */
  deleteOrder: async (id: number): Promise<ApiResponseDto<null>> => {
    return apiRequest.delete(`/user/orders/${id}`);
  },
};
