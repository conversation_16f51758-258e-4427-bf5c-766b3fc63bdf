import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Typography,
  FormItem,
  Input,
  Select,
  Textarea,
  Switch,
} from '@/shared/components/common';
import type { DigitalDeliveryDto } from '../../types/order.types';

interface DigitalDeliveryFormProps {
  delivery?: DigitalDeliveryDto;
  onDeliveryChange: (delivery: DigitalDeliveryDto) => void;
  customerEmail?: string;
  customerPhone?: string;
}

/**
 * Form thông tin giao hàng sản phẩm số
 */
const DigitalDeliveryForm: React.FC<DigitalDeliveryFormProps> = ({
  delivery,
  onDeliveryChange,
  customerEmail,
  customerPhone,
}) => {
  const { t } = useTranslation(['business', 'common']);

  // Delivery method options
  const deliveryMethodOptions = [
    { value: 'email', label: t('business:order.digital.email') },
    { value: 'sms', label: t('business:order.digital.sms') },
    { value: 'download', label: t('business:order.digital.download') },
    { value: 'both', label: t('business:order.digital.both') },
  ];

  // Xử lý thay đổi phương thức giao hàng
  const handleMethodChange = (value: string | number | string[] | number[]) => {
    const method = Array.isArray(value) ? String(value[0]) : String(value);
    onDeliveryChange({
      ...delivery,
      method,
    });
  };

  // Xử lý thay đổi email giao hàng
  const handleEmailChange = (email: string) => {
    onDeliveryChange({
      ...delivery,
      email,
    });
  };

  // Xử lý thay đổi số điện thoại
  const handlePhoneChange = (phone: string) => {
    onDeliveryChange({
      ...delivery,
      phone,
    });
  };

  // Xử lý thay đổi ghi chú giao hàng
  const handleNotesChange = (notes: string) => {
    onDeliveryChange({
      ...delivery,
      notes,
    });
  };

  // Xử lý thay đổi tự động giao hàng
  const handleAutoDeliveryChange = (autoDelivery: boolean) => {
    onDeliveryChange({
      ...delivery,
      autoDelivery,
    });
  };

  // Xử lý thay đổi thời gian giao hàng
  const handleDeliveryTimeChange = (deliveryTime: string) => {
    onDeliveryChange({
      ...delivery,
      deliveryTime,
    });
  };

  return (
    <Card>
      <div className="p-6">
        <div className="flex items-center gap-2 mb-4">
          <Typography variant="h6">
            {t('business:order.digitalDelivery')}
          </Typography>
        </div>

        <div className="space-y-4">
          {/* Phương thức giao hàng */}
          <FormItem
            label={t('business:order.digital.method')}
            required
          >
            <Select
              value={delivery?.method || ''}
              onChange={handleMethodChange}
              options={deliveryMethodOptions}
              placeholder={t('business:order.digital.selectMethod')}
              fullWidth
            />
          </FormItem>

          {/* Email giao hàng */}
          {(delivery?.method === 'email' || delivery?.method === 'both') && (
            <FormItem
              label={t('business:order.digital.email')}
              required
            >
              <div>
                <Input
                  type="email"
                  value={delivery?.email || customerEmail || ''}
                  onChange={(e) => handleEmailChange(e.target.value)}
                  placeholder={t('business:order.digital.emailPlaceholder')}
                  fullWidth
                />
                {customerEmail && (
                  <Typography variant="caption" className="text-gray-500 mt-1">
                    {t('business:order.digital.customerEmailHint')}
                  </Typography>
                )}
              </div>
            </FormItem>
          )}

          {/* Số điện thoại */}
          {(delivery?.method === 'sms' || delivery?.method === 'both') && (
            <FormItem
              label={t('business:order.digital.phone')}
              required
            >
              <div>
                <Input
                  value={delivery?.phone || customerPhone || ''}
                  onChange={(e) => handlePhoneChange(e.target.value)}
                  placeholder={t('business:order.digital.phonePlaceholder')}
                  fullWidth
                />
                {customerPhone && (
                  <Typography variant="caption" className="text-gray-500 mt-1">
                    {t('business:order.digital.customerPhoneHint')}
                  </Typography>
                )}
              </div>
            </FormItem>
          )}

          {/* Tự động giao hàng */}
          <FormItem
            label={t('business:order.digital.autoDelivery')}
          >
            <div className="flex items-center gap-3">
              <Switch
                checked={delivery?.autoDelivery || false}
                onChange={handleAutoDeliveryChange}
              />
              <Typography variant="body2" className="text-gray-600">
                {t('business:order.digital.autoDeliveryDescription')}
              </Typography>
            </div>
          </FormItem>

          {/* Thời gian giao hàng (nếu không tự động) */}
          {!delivery?.autoDelivery && (
            <FormItem
              label={t('business:order.digital.deliveryTime')}
            >
              <div>
                <Input
                  type="datetime-local"
                  value={delivery?.deliveryTime || ''}
                  onChange={(e) => handleDeliveryTimeChange(e.target.value)}
                  fullWidth
                />
                <Typography variant="caption" className="text-gray-500 mt-1">
                  {t('business:order.digital.deliveryTimeHint')}
                </Typography>
              </div>
            </FormItem>
          )}

          {/* Ghi chú giao hàng */}
          <FormItem
            label={t('business:order.digital.notes')}
          >
            <Textarea
              value={delivery?.notes || ''}
              onChange={(e) => handleNotesChange(e.target.value)}
              placeholder={t('business:order.digital.notesPlaceholder')}
              rows={2}
              fullWidth
            />
          </FormItem>

          {/* Thông tin bổ sung */}
          <div className="p-3 bg-blue-50 rounded-lg">
            <Typography variant="subtitle2" className="mb-2 text-blue-800">
              {t('business:order.digital.deliveryInfo')}
            </Typography>
            <ul className="space-y-1 text-sm text-blue-600">
              <li>• {t('business:order.digital.deliveryInfoItem1')}</li>
              <li>• {t('business:order.digital.deliveryInfoItem2')}</li>
              <li>• {t('business:order.digital.deliveryInfoItem3')}</li>
            </ul>
          </div>
        </div>
      </div>
    </Card>
  );
};

export default DigitalDeliveryForm;
