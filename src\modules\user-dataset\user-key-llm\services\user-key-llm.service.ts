import { apiClient } from '@/shared/api/axios';
import {
  CreateUserKeyLlmDto,
  UpdateUserKeyLlmDto,
  UserKeyLlmResponseDto,
  UserKeyLlmDetailResponseDto,
  UserKeyLlmQueryDto,
  TestKeyDto,
  TestKeyResponseDto,
  KeyUsageStatsResponseDto,
  PaginatedResult,
} from '../types/user-key-llm.types';

const API_BASE_URL = '/user/key-llm';

/**
 * Lấy danh sách key LLM có phân trang
 * @param queryDto Tham số truy vấn
 * @returns Danh sách key LLM với phân trang
 */
export const getUserKeyLlmList = async (
  queryDto?: UserKeyLlmQueryDto
): Promise<PaginatedResult<UserKeyLlmResponseDto>> => {
  const queryParams = new URLSearchParams();

  if (queryDto?.page) queryParams.append('page', queryDto.page.toString());
  if (queryDto?.limit) queryParams.append('limit', queryDto.limit.toString());
  if (queryDto?.search) queryParams.append('search', queryDto.search);
  if (queryDto?.provider) queryParams.append('provider', queryDto.provider);
  if (queryDto?.status) queryParams.append('status', queryDto.status);
  if (queryDto?.sortBy) queryParams.append('sortBy', queryDto.sortBy);
  if (queryDto?.sortDirection) queryParams.append('sortDirection', queryDto.sortDirection);

  const queryString = queryParams.toString();
  const url = queryString ? `${API_BASE_URL}?${queryString}` : API_BASE_URL;

  const response = await apiClient.get<PaginatedResult<UserKeyLlmResponseDto>>(url);
  return response.result;
};

/**
 * Lấy chi tiết key LLM
 * @param id ID của key LLM
 * @returns Thông tin chi tiết key LLM
 */
export const getUserKeyLlmDetail = async (id: string): Promise<UserKeyLlmDetailResponseDto> => {
  const response = await apiClient.get<UserKeyLlmDetailResponseDto>(`${API_BASE_URL}/${id}`);
  return response.result;
};

/**
 * Tạo key LLM mới
 * @param data Dữ liệu tạo key
 * @returns Key LLM đã tạo
 */
export const createUserKeyLlm = async (
  data: CreateUserKeyLlmDto
): Promise<UserKeyLlmResponseDto> => {
  const response = await apiClient.post<UserKeyLlmResponseDto>(API_BASE_URL, data);
  return response.result;
};

/**
 * Cập nhật key LLM
 * @param id ID của key LLM
 * @param data Dữ liệu cập nhật
 * @returns Key LLM đã cập nhật
 */
export const updateUserKeyLlm = async (
  id: string,
  data: UpdateUserKeyLlmDto
): Promise<UserKeyLlmResponseDto> => {
  const response = await apiClient.put<UserKeyLlmResponseDto>(`${API_BASE_URL}/${id}`, data);
  return response.result;
};

/**
 * Xóa key LLM
 * @param id ID của key LLM
 */
export const deleteUserKeyLlm = async (id: string): Promise<void> => {
  await apiClient.delete(`${API_BASE_URL}/${id}`);
};

/**
 * Test key LLM
 * @param id ID của key LLM
 * @param data Dữ liệu test
 * @returns Kết quả test
 */
export const testUserKeyLlm = async (
  id: string,
  data: TestKeyDto = {}
): Promise<TestKeyResponseDto> => {
  const response = await apiClient.post<TestKeyResponseDto>(`${API_BASE_URL}/${id}/test`, data);
  return response.result;
};

/**
 * Lấy thống kê sử dụng key
 * @param id ID của key LLM
 * @returns Thống kê sử dụng
 */
export const getUserKeyLlmUsageStats = async (id: string): Promise<KeyUsageStatsResponseDto> => {
  const response = await apiClient.get<KeyUsageStatsResponseDto>(
    `${API_BASE_URL}/${id}/usage-stats`
  );
  return response.result;
};

/**
 * Kích hoạt key
 * @param id ID của key LLM
 * @returns Key LLM đã kích hoạt
 */
export const activateUserKeyLlm = async (id: string): Promise<UserKeyLlmResponseDto> => {
  const response = await apiClient.post<UserKeyLlmResponseDto>(`${API_BASE_URL}/${id}/activate`);
  return response.result;
};

/**
 * Vô hiệu hóa key
 * @param id ID của key LLM
 * @returns Key LLM đã vô hiệu hóa
 */
export const deactivateUserKeyLlm = async (id: string): Promise<UserKeyLlmResponseDto> => {
  const response = await apiClient.post<UserKeyLlmResponseDto>(`${API_BASE_URL}/${id}/deactivate`);
  return response.result;
};

/**
 * Làm mới key (regenerate)
 * @param id ID của key LLM
 * @returns Key LLM đã làm mới
 */
export const regenerateUserKeyLlm = async (id: string): Promise<UserKeyLlmResponseDto> => {
  const response = await apiClient.post<UserKeyLlmResponseDto>(`${API_BASE_URL}/${id}/regenerate`);
  return response.result;
};

/**
 * Nhân bản key
 * @param id ID của key LLM
 * @param name Tên key mới
 * @returns Key LLM đã nhân bản
 */
export const duplicateUserKeyLlm = async (
  id: string,
  name: string
): Promise<UserKeyLlmResponseDto> => {
  const response = await apiClient.post<UserKeyLlmResponseDto>(`${API_BASE_URL}/${id}/duplicate`, {
    name,
  });
  return response.result;
};

/**
 * Lấy danh sách models có sẵn cho provider
 * @param provider Nhà cung cấp
 * @returns Danh sách models
 */
export const getAvailableModels = async (provider: string): Promise<string[]> => {
  const response = await apiClient.get<string[]>(`${API_BASE_URL}/models/${provider}`);
  return response.result;
};

/**
 * Validate key format
 * @param provider Nhà cung cấp
 * @param apiKey API key cần validate
 * @returns Kết quả validation
 */
export const validateKeyFormat = async (
  provider: string,
  apiKey: string
): Promise<{ valid: boolean; message: string }> => {
  const response = await apiClient.post<{ valid: boolean; message: string }>(
    `${API_BASE_URL}/validate-format`,
    { provider, apiKey }
  );
  return response.result;
};

/**
 * Lấy thông tin quota/billing cho key
 * @param id ID của key LLM
 * @returns Thông tin quota và billing
 */
export const getKeyQuotaInfo = async (
  id: string
): Promise<{
  quota: {
    total: number;
    used: number;
    remaining: number;
  };
  billing: {
    currentPeriod: {
      start: string;
      end: string;
      usage: number;
      cost: number;
    };
  };
}> => {
  const response = await apiClient.get(`${API_BASE_URL}/${id}/quota`);
  return response.result as {
    quota: {
      total: number;
      used: number;
      remaining: number;
    };
    billing: {
      currentPeriod: {
        start: string;
        end: string;
        usage: number;
        cost: number;
      };
    };
  };
};

/**
 * Export keys (for backup)
 * @returns File blob
 */
export const exportUserKeyLlm = async (): Promise<Blob> => {
  const response = await apiClient.get(`${API_BASE_URL}/export`, {
    responseType: 'blob',
  });
  return response.result as Blob;
};

/**
 * Import keys (from backup)
 * @param file File backup
 * @returns Kết quả import
 */
export const importUserKeyLlm = async (
  file: File
): Promise<{
  imported: number;
  failed: number;
  errors: string[];
}> => {
  const formData = new FormData();
  formData.append('file', file);

  const response = await apiClient.post(`${API_BASE_URL}/import`, formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
  return response.result as {
    imported: number;
    failed: number;
    errors: string[];
  };
};

/**
 * Batch delete keys
 * @param ids Danh sách ID keys
 * @returns Kết quả xóa
 */
export const batchDeleteUserKeyLlm = async (
  ids: string[]
): Promise<{
  deleted: number;
  failed: number;
  errors: string[];
}> => {
  const response = await apiClient.post(`${API_BASE_URL}/batch/delete`, { ids });
  return response.result as {
    deleted: number;
    failed: number;
    errors: string[];
  };
};

/**
 * Batch activate keys
 * @param ids Danh sách ID keys
 * @returns Kết quả kích hoạt
 */
export const batchActivateUserKeyLlm = async (
  ids: string[]
): Promise<{
  activated: number;
  failed: number;
  errors: string[];
}> => {
  const response = await apiClient.post(`${API_BASE_URL}/batch/activate`, { ids });
  return response.result as {
    activated: number;
    failed: number;
    errors: string[];
  };
};

/**
 * Batch deactivate keys
 * @param ids Danh sách ID keys
 * @returns Kết quả vô hiệu hóa
 */
export const batchDeactivateUserKeyLlm = async (
  ids: string[]
): Promise<{
  deactivated: number;
  failed: number;
  errors: string[];
}> => {
  const response = await apiClient.post(`${API_BASE_URL}/batch/deactivate`, { ids });
  return response.result as {
    deactivated: number;
    failed: number;
    errors: string[];
  };
};

/**
 * User Key LLM Service object
 */
const UserKeyLlmService = {
  // Query methods
  getList: getUserKeyLlmList,
  getById: getUserKeyLlmDetail,
  getUsageStats: getUserKeyLlmUsageStats,
  getQuotaInfo: getKeyQuotaInfo,
  getAvailableModels,

  // Mutation methods
  create: createUserKeyLlm,
  update: updateUserKeyLlm,
  delete: deleteUserKeyLlm,
  testKey: testUserKeyLlm,
  activate: activateUserKeyLlm,
  deactivate: deactivateUserKeyLlm,
  regenerate: regenerateUserKeyLlm,
  duplicate: duplicateUserKeyLlm,
  validateKeyFormat,

  // Import/Export methods
  exportKeys: exportUserKeyLlm,
  importKeys: importUserKeyLlm,

  // Batch methods
  batchDelete: batchDeleteUserKeyLlm,
  batchActivate: batchActivateUserKeyLlm,
  batchDeactivate: batchDeactivateUserKeyLlm,
};

export default UserKeyLlmService;
